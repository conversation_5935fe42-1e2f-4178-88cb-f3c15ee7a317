import pdfplumber
import re
from collections import Counter
from pathlib import Path
import json

# Common English stop words to exclude
stop_words = {
    'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'from',
    'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did',
    'will', 'would', 'should', 'could', 'may', 'might', 'must', 'can', 'shall',
    'i', 'you', 'he', 'she', 'it', 'we', 'they', 'me', 'him', 'her', 'us', 'them',
    'my', 'your', 'his', 'her', 'its', 'our', 'their', 'this', 'that', 'these', 'those',
    'what', 'which', 'who', 'when', 'where', 'why', 'how',
    'as', 'if', 'so', 'than', 'then', 'not', 'no', 'yes', 'all', 'each', 'every', 'both',
    'some', 'any', 'few', 'more', 'most', 'other', 'such', 'only', 'just', 'also', 'too',
    'very', 'too', 'much', 'many', 'more', 'less', 'most', 'least', 'well', 'good', 'bad',
    'up', 'down', 'out', 'in', 'on', 'off', 'over', 'under', 'above', 'below', 'through',
    'during', 'before', 'after', 'between', 'among', 'around', 'about', 'near', 'far',
    'here', 'there', 'where', 'now', 'then', 'today', 'yesterday', 'tomorrow',
    'p', 'pp', 'page', 'pages', 'figure', 'figures', 'fig', 'table', 'tables', 'etc',
    'e', 'g', 'i', 'v', 'vs', 'vs', 'et', 'al', 'see', 'also', 'note', 'notes'
}

# Extract text from all PDFs
text_data = []
pdf_files = sorted(Path('.').glob('*.pdf'))

print(f"Found {len(pdf_files)} PDF files")

for pdf_file in pdf_files:
    try:
        with pdfplumber.open(pdf_file) as pdf:
            for page in pdf.pages:
                text = page.extract_text()
                if text:
                    text_data.append(text)
        print(f"Extracted: {pdf_file.name}")
    except Exception as e:
        print(f"Error processing {pdf_file.name}: {e}")

# Combine all text
all_text = ' '.join(text_data)

# Tokenize: convert to lowercase, extract words (including hyphenated words)
words = re.findall(r'\b[a-z]+(?:-[a-z]+)?\b', all_text.lower())

# Filter out stop words and short words
technical_words = [w for w in words if w not in stop_words and len(w) > 3]

# Count word frequencies
word_freq = Counter(technical_words)

# Get top 500 words
top_500 = word_freq.most_common(500)

# Save results
results = []
for word, count in top_500:
    results.append({
        'word': word,
        'frequency': count,
        'chinese': '',
        'example': ''
    })

with open('top_500_technical_terms.json', 'w', encoding='utf-8') as f:
    json.dump(results, f, ensure_ascii=False, indent=2)

print(f"\nExtracted {len(words)} total words")
print(f"Found {len(technical_words)} technical words (after filtering)")
print(f"Found {len(word_freq)} unique technical words")
print(f"Top 20 technical terms:")
for word, count in top_500[:20]:
    print(f"  {word}: {count}")

print("\nResults saved to top_500_technical_terms.json")
