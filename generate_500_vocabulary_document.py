#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Generate comprehensive 500-word vocabulary document
"""

import json
from pathlib import Path

# Load merged 500-word vocabulary
with open('ocred/vocabulary_500_merged.json', encoding='utf-8') as f:
    vocab_data = json.load(f)

# Calculate statistics
total_words = len(vocab_data)
total_frequency = sum(item['frequency'] for item in vocab_data)
translated = sum(1 for item in vocab_data if '需要人工翻译' not in item['translation'])

# Calculate coverage
top10_freq = sum(item['frequency'] for item in vocab_data[:10])
top20_freq = sum(item['frequency'] for item in vocab_data[:20])
top50_freq = sum(item['frequency'] for item in vocab_data[:50])
top100_freq = sum(item['frequency'] for item in vocab_data[:100])
top200_freq = sum(item['frequency'] for item in vocab_data[:200])

# Generate document
doc = f"""# 📚 HET专业词汇大全 - 500词完整版
## HET Professional Vocabulary - Complete 500 Words Edition

**文档版本**: v2.0 (扩展至500词)  
**生成时间**: 2026年2月17日  
**数据来源**: 39个PDF文档，共2,177,809字符

---

## 📊 数据统计概览

### 核心数据

| 指标 | 数值 |
|------|------|
| 📖 词汇总数 | {total_words} |
| 🔢 总出现频次 | {total_frequency:,} |
| ✅ 已翻译词汇 | {translated} |
| ⏳ 待翻译词汇 | {total_words - translated} |
| 📈 翻译完成度 | {translated/total_words*100:.1f}% |
| 📄 源文档数量 | 39个PDF |
| 📝 源文本字符数 | 2,177,809 |

### 覆盖率分析

| 词汇范围 | 出现频次 | 占比 | 累计占比 |
|---------|---------|------|---------|
| Top 10 | {top10_freq:,} | {top10_freq/total_frequency*100:.1f}% | {top10_freq/total_frequency*100:.1f}% |
| Top 20 | {top20_freq:,} | {top20_freq/total_frequency*100:.1f}% | {top20_freq/total_frequency*100:.1f}% |
| Top 50 | {top50_freq:,} | {top50_freq/total_frequency*100:.1f}% | {top50_freq/total_frequency*100:.1f}% |
| Top 100 | {top100_freq:,} | {top100_freq/total_frequency*100:.1f}% | {top100_freq/total_frequency*100:.1f}% |
| Top 200 | {top200_freq:,} | {top200_freq/total_frequency*100:.1f}% | {top200_freq/total_frequency*100:.1f}% |
| Top 500 | {total_frequency:,} | 100.0% | 100.0% |

**关键发现**:
- ⭐ 前50个词汇覆盖 **{top50_freq/total_frequency*100:.1f}%** 的内容
- ⭐ 前100个词汇覆盖 **{top100_freq/total_frequency*100:.1f}%** 的内容
- ⭐ 前200个词汇覆盖 **{top200_freq/total_frequency*100:.1f}%** 的内容

---

## 🎯 学习建议

### 分阶段学习计划

| 阶段 | 词汇数量 | 覆盖率 | 学习时间 | 适合人群 |
|------|---------|--------|---------|---------|
| 入门 | 前20个 | {top20_freq/total_frequency*100:.1f}% | 2小时 | 初学者 |
| 基础 | 前50个 | {top50_freq/total_frequency*100:.1f}% | 5小时 | 进阶者 |
| 进阶 | 前100个 | {top100_freq/total_frequency*100:.1f}% | 10小时 | 专业人员 |
| 精通 | 前200个 | {top200_freq/total_frequency*100:.1f}% | 20小时 | 技术专家 |
| 专家 | 全部500个 | 100.0% | 50小时 | 行业专家 |

---

## 📖 完整词汇表

### 使用说明
- **排名**: 按出现频率排序
- **频率**: 在所有文档中出现的次数
- **翻译**: 中文翻译（✅已翻译 / ⏳待翻译）
- **例句**: 实际使用示例

---

"""

# Add vocabulary entries in groups of 50
for i in range(0, total_words, 50):
    group_num = i // 50 + 1
    group_start = i + 1
    group_end = min(i + 50, total_words)
    
    doc += f"### 第{group_num}组: 词汇 {group_start}-{group_end}\n\n"
    
    for item in vocab_data[i:group_end]:
        rank = item['rank']
        word = item['word']
        freq = item['frequency']
        translation = item['translation']
        example = item['example']
        
        # Mark if translated
        status = "✅" if '需要人工翻译' not in translation else "⏳"
        
        doc += f"#### {rank}. {word.upper()} {status}\n\n"
        doc += f"- **频率**: {freq:,} 次\n"
        doc += f"- **翻译**: {translation}\n"
        doc += f"- **例句**: {example}\n\n"
    
    doc += "---\n\n"

# Add footer
doc += f"""
## 📈 使用统计

### 翻译完成度

- ✅ 已完成翻译: {translated}/{total_words} ({translated/total_words*100:.1f}%)
- ⏳ 待完成翻译: {total_words - translated}/{total_words} ({(total_words - translated)/total_words*100:.1f}%)

### 学习效率分析

根据词频分布，建议优先学习前100个词汇，可以覆盖近50%的专业内容。

---

**文档版本**: v2.0  
**最后更新**: 2026-02-17  
**词汇数量**: 500个

---

*© 2026 HET Professional Vocabulary - 500 Words Complete Edition*
"""

# Save document
output_file = Path("HET专业词汇大全_500词完整版.md")
with open(output_file, 'w', encoding='utf-8') as f:
    f.write(doc)

print(f"✓ Generated 500-word vocabulary document")
print(f"  File: {output_file}")
print(f"  Size: {len(doc):,} characters")
print(f"  Words: {total_words}")
print(f"  Translated: {translated} ({translated/total_words*100:.1f}%)")
print(f"\n✓ Complete!")

