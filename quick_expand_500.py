#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Quick expansion to 500 words - simplified version
"""

import json
import re
from pathlib import Path
from collections import Counter

print("Loading extracted text...")
text_file = Path('ocred/all_extracted_text.txt')
with open(text_file, 'r', encoding='utf-8') as f:
    text = f.read()

print(f"Text loaded: {len(text):,} characters")

# Expanded stop words
STOP_WORDS = {
    'the', 'be', 'to', 'of', 'and', 'a', 'in', 'that', 'have', 'i',
    'it', 'for', 'not', 'on', 'with', 'he', 'as', 'you', 'do', 'at',
    'this', 'but', 'his', 'by', 'from', 'they', 'we', 'say', 'her', 'she',
    'or', 'an', 'will', 'my', 'one', 'all', 'would', 'there', 'their', 'what',
    'so', 'up', 'out', 'if', 'about', 'who', 'get', 'which', 'go', 'me',
    'when', 'make', 'can', 'like', 'time', 'no', 'just', 'him', 'know', 'take',
    'people', 'into', 'year', 'your', 'good', 'some', 'could', 'them', 'see', 'other',
    'than', 'then', 'now', 'look', 'only', 'come', 'its', 'over', 'think', 'also',
    'back', 'after', 'use', 'two', 'how', 'our', 'work', 'first', 'well', 'way',
    'even', 'new', 'want', 'because', 'any', 'these', 'give', 'day', 'most', 'us',
    'is', 'was', 'are', 'been', 'has', 'had', 'were', 'said', 'did', 'having',
    'may', 'should', 'must', 'shall', 'being', 'does', 'am',
    'alberta', 'province', 'queen', 'majesty', 'crown', 'government', 'act',
    'regulation', 'section', 'subsection', 'paragraph', 'clause', 'minister',
    'director', 'inspector', 'person', 'means', 'include', 'includes',
    'right', 'left', 'front', 'rear', 'side', 'each', 'every', 'such',
    'following', 'accordance', 'respect', 'pursuant', 'prescribed', 'required',
    'page', 'figure', 'table', 'note', 'see', 'refer', 'reference',
    'chapter', 'part', 'appendix', 'schedule',
    'made', 'between', 'during', 'without', 'before', 'under', 'around',
    'however', 'therefore', 'thus', 'where', 'whether', 'while', 'within',
    'through', 'upon', 'against', 'among', 'both', 'either', 'neither',
    'same', 'different', 'another', 'next', 'last', 'many', 'much', 'more',
    'less', 'few', 'several', 'various', 'certain', 'particular', 'general',
    'specific', 'appropriate', 'applicable', 'relevant', 'related',
}

print("Extracting words...")
words = re.findall(r'\b[a-zA-Z]{3,}\b', text.lower())
print(f"Total words: {len(words):,}")

print("Filtering stop words...")
filtered_words = [w for w in words if w not in STOP_WORDS]
print(f"After filtering: {len(filtered_words):,}")

print("Counting frequency...")
word_freq = Counter(filtered_words)
print(f"Unique words: {len(word_freq):,}")

print("Getting top 500...")
top_500 = word_freq.most_common(500)

print("Creating vocabulary data (without examples for speed)...")
vocabulary_data = []
for rank, (word, frequency) in enumerate(top_500, 1):
    vocabulary_data.append({
        'rank': rank,
        'word': word,
        'frequency': frequency,
        'translation': '需要人工翻译',
        'example': f'This technical term appears {frequency} times in the documents.'
    })

# Save to JSON
output_file = Path('ocred/vocabulary_500.json')
with open(output_file, 'w', encoding='utf-8') as f:
    json.dump(vocabulary_data, f, ensure_ascii=False, indent=2)

print(f"\n✓ Saved to: {output_file}")

# Statistics
total_freq = sum(item['frequency'] for item in vocabulary_data)
print(f"\n{'='*70}")
print("STATISTICS")
print(f"{'='*70}")
print(f"Total frequency: {total_freq:,}")
print(f"Top 10: {sum(item['frequency'] for item in vocabulary_data[:10])/total_freq*100:.1f}%")
print(f"Top 50: {sum(item['frequency'] for item in vocabulary_data[:50])/total_freq*100:.1f}%")
print(f"Top 100: {sum(item['frequency'] for item in vocabulary_data[:100])/total_freq*100:.1f}%")
print(f"Top 200: {sum(item['frequency'] for item in vocabulary_data[:200])/total_freq*100:.1f}%")
print(f"Top 500: 100.0%")
print(f"{'='*70}")

print("\nTop 20:")
for item in vocabulary_data[:20]:
    print(f"{item['rank']:3d}. {item['word']:20s} - {item['frequency']:,}")

print("\n✓ Complete!")

