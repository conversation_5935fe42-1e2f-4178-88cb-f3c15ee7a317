#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Generate additional study materials from vocabulary data
"""

import json
import random
from pathlib import Path

# Load vocabulary data
with open('ocred/vocabulary_ultimate.json', encoding='utf-8') as f:
    vocab_data = json.load(f)

print(f"Loaded {len(vocab_data)} vocabulary terms")

# Generate flashcards document
flashcards_md = """# 🎴 HET词汇学习卡片
## Vocabulary Flashcards for Quick Learning

本文档包含200个HET专业词汇的学习卡片，适合快速记忆和复习。

---

## 📚 使用方法

1. **遮挡法**：遮住中文翻译，看英文单词回忆中文意思
2. **反向学习**：遮住英文单词，看中文翻译回忆英文单词
3. **例句填空**：遮住关键词，根据例句填空
4. **分组学习**：每次学习20-30个词汇

---

## 🎯 高频词汇卡片 (Top 50)

"""

# Add top 50 flashcards
for item in vocab_data[:50]:
    flashcards_md += f"""
### 卡片 #{item['rank']}

**英文：** {item['word'].upper()}  
**中文：** {item['translation']}  
**频率：** {item['frequency']}次

**例句：**  
> {item['example'].replace(item['word'], '______').replace(item['word'].upper(), '______').replace(item['word'].capitalize(), '______')}

**答案：** {item['word']}

---
"""

flashcards_md += """
## 📝 中频词汇卡片 (51-100)

"""

for item in vocab_data[50:100]:
    flashcards_md += f"""
### 卡片 #{item['rank']}

**英文：** {item['word'].upper()}  
**中文：** {item['translation']}  
**频率：** {item['frequency']}次

---
"""

flashcards_md += """
## 📖 低频词汇卡片 (101-200)

"""

for item in vocab_data[100:200]:
    flashcards_md += f"**{item['rank']}.** {item['word'].upper()} = {item['translation']} ({item['frequency']}次)  \n"

flashcards_md += """

---

## 💡 学习建议

### 记忆技巧
1. **分组记忆**：每天学习20个新词汇
2. **定期复习**：使用间隔重复法（1天、3天、7天、14天）
3. **实际应用**：在工作中主动使用这些词汇
4. **联想记忆**：将词汇与实际物品或场景联系

### 学习计划
- **第1周**：学习前50个高频词汇
- **第2周**：复习前50个，学习51-100
- **第3周**：复习前100个，学习101-150
- **第4周**：复习前150个，学习151-200
- **第5周**：全面复习所有200个词汇

---

*© 2026 HET Vocabulary Flashcards*
"""

# Save flashcards
flashcards_file = Path("HET词汇学习卡片.md")
with open(flashcards_file, 'w', encoding='utf-8') as f:
    f.write(flashcards_md)

print(f"✓ Generated flashcards: {flashcards_file}")

# Generate quiz document
quiz_md = """# 📝 HET词汇测试题
## Vocabulary Quiz and Exercises

本文档包含多种类型的词汇测试题，帮助检验学习效果。

---

## 📋 测试说明

- **总题数**：100题
- **题型**：选择题、填空题、翻译题
- **建议时间**：60分钟
- **及格分数**：70分

---

## 📝 第一部分：英译中选择题 (1-30题)

每题1分，共30分。

"""

# Generate 30 multiple choice questions
for i in range(30):
    item = vocab_data[i]
    # Create wrong answers
    wrong_answers = random.sample([v for v in vocab_data if v != item], 3)
    all_answers = [item] + wrong_answers
    random.shuffle(all_answers)
    
    quiz_md += f"""
**{i+1}.** {item['word'].upper()} 的中文意思是？

A. {all_answers[0]['translation']}  
B. {all_answers[1]['translation']}  
C. {all_answers[2]['translation']}  
D. {all_answers[3]['translation']}

"""

quiz_md += """
---

## 📝 第二部分：中译英选择题 (31-60题)

每题1分，共30分。

"""

for i in range(30, 60):
    item = vocab_data[i]
    wrong_answers = random.sample([v for v in vocab_data if v != item], 3)
    all_answers = [item] + wrong_answers
    random.shuffle(all_answers)
    
    quiz_md += f"""
**{i+1}.** "{item['translation']}" 的英文是？

A. {all_answers[0]['word']}  
B. {all_answers[1]['word']}  
C. {all_answers[2]['word']}  
D. {all_answers[3]['word']}

"""

quiz_md += """
---

## 📝 第三部分：填空题 (61-80题)

每题2分，共40分。根据例句填入正确的英文单词。

"""

for i in range(60, 80):
    item = vocab_data[i]
    example = item['example']
    # Replace the word with blank
    blank_example = example.replace(item['word'], '______').replace(item['word'].upper(), '______').replace(item['word'].capitalize(), '______')
    
    quiz_md += f"""
**{i+1}.** {blank_example}

答案：______________

"""

quiz_md += """
---

## 📝 答案部分

### 第一部分答案 (1-30题)

"""

for i in range(30):
    item = vocab_data[i]
    quiz_md += f"{i+1}. {item['translation']}  \n"

quiz_md += """
### 第二部分答案 (31-60题)

"""

for i in range(30, 60):
    item = vocab_data[i]
    quiz_md += f"{i+1}. {item['word']}  \n"

quiz_md += """
### 第三部分答案 (61-80题)

"""

for i in range(60, 80):
    item = vocab_data[i]
    quiz_md += f"{i+1}. {item['word']}  \n"

quiz_md += """

---

## 📊 评分标准

| 分数范围 | 等级 | 评价 |
|---------|------|------|
| 90-100 | 优秀 | 词汇掌握非常好 |
| 80-89 | 良好 | 词汇掌握较好 |
| 70-79 | 及格 | 基本掌握词汇 |
| 60-69 | 不及格 | 需要加强学习 |
| <60 | 差 | 需要重新学习 |

---

*© 2026 HET Vocabulary Quiz*
"""

# Save quiz
quiz_file = Path("HET词汇测试题.md")
with open(quiz_file, 'w', encoding='utf-8') as f:
    f.write(quiz_md)

print(f"✓ Generated quiz: {quiz_file}")

print("\n" + "="*70)
print("✅ ALL STUDY MATERIALS GENERATED!")
print("="*70)
print(f"📄 Flashcards: {flashcards_file}")
print(f"📝 Quiz: {quiz_file}")
print("="*70)

