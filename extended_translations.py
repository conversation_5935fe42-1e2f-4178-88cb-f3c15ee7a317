#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Extended translation dictionary for HET vocabulary
"""

EXTENDED_TRANSLATIONS = {
    # Basic terms
    'trailer': '拖车、挂车',
    'used': '使用的、二手的',
    'components': '部件、组件',
    'brakes': '制动器（复数）',
    'courtesy': '礼貌、提供',
    'force': '力、力量',
    'load': '负载、载荷',
    'shown': '显示的',
    'type': '类型',
    'objective': '目标、目的',
    'flow': '流动、流量',
    'frame': '框架、车架',
    'systems': '系统（复数）',
    'operation': '操作、运行',
    'axle': '车轴',
    'using': '使用',
    'reservoir': '储液罐、油箱',
    'check': '检查',
    'position': '位置',
    'test': '测试',
    'line': '管路、线路',
    'high': '高的',
    'supply': '供应',
    'material': '材料',
    'shows': '显示',
    'side': '侧面、边',
    'open': '打开',
    'tractor': '牵引车',
    'drum': '鼓、制动鼓',
    'surface': '表面',
    'area': '区域、面积',
    'following': '以下的',
    'example': '例子',
    'able': '能够',
    'types': '类型（复数）',
    'common': '常见的',
    'braking': '制动',
    'inlet': '进口',
    'information': '信息',
    'number': '数字、编号',
    'off': '关闭',
    'port': '端口',
    'ensure': '确保',
    'provide': '提供',
    'application': '应用',
    'applied': '应用的',
    'lines': '管路（复数）',
    'heat': '热量',
    'heavy': '重型的',
    'steel': '钢',
    'cause': '原因、导致',
    'amount': '数量',
    'psi': '磅/平方英寸（压力单位）',
    'end': '末端',
    'inch': '英寸',
    'three': '三',
    'chamber': '腔室',
    'unit': '单元、装置',
    'valves': '阀门（复数）',
    'materials': '材料（复数）',
    'connected': '连接的',
    'plate': '板、盘',
    'small': '小的',
    'signal': '信号',
    'kpa': '千帕（压力单位）',
    'speed': '速度',
    'remove': '移除',
    'parking': '停车',
    'assembly': '组装、总成',
    'release': '释放',
    'manufacturer': '制造商',
    'motor': '电机、马达',
    'low': '低的',
    'conductor': '导体',
    'self': '自身',
    'parallel': '平行的',
    'completed': '完成的',
    'procedures': '程序（复数）',
    'drive': '驱动',
    'located': '位于',
    'size': '尺寸',
    'installed': '安装的',
    'length': '长度',
    'design': '设计',
    'direction': '方向',
    'moving': '移动的',
    'return': '返回',
    'outlet': '出口',
    'spring': '弹簧',
    'power': '功率、动力',
    'section': '部分、截面',
    'required': '需要的',
    'distance': '距离',
    'diameter': '直径',
    'capacity': '容量',
    'maximum': '最大的',
    'minimum': '最小的',
    'temperature': '温度',
    'weight': '重量',
    'height': '高度',
    'width': '宽度',
    'depth': '深度',
    'thickness': '厚度',
    'angle': '角度',
    'ratio': '比率',
    'rate': '速率',
    'level': '水平、液位',
    'range': '范围',
    'limit': '限制',
    'value': '值',
    'reading': '读数',
    'measure': '测量',
    'scale': '刻度、比例',
    'indicator': '指示器',
    'display': '显示',
    'monitor': '监控',
    'sensor': '传感器',
    'detector': '检测器',
    'switch': '开关',
    'button': '按钮',
    'lever': '杠杆',
    'handle': '手柄',
    'knob': '旋钮',
    'pedal': '踏板',
    'rod': '杆',
    'pin': '销',
    'key': '键、钥匙',
    'lock': '锁',
    'latch': '闩锁',
    'catch': '卡扣',
    'clip': '夹子',
    'ring': '环',
    'collar': '套环',
    'sleeve': '套筒',
    'bushing': '衬套',
    'spacer': '垫片',
    'shim': '垫片',
    'insert': '插入件',
    'adapter': '适配器',
    'fitting': '接头',
    'coupling': '联轴器',
    'joint': '接头、关节',
    'union': '活接头',
    'elbow': '弯头',
    'tee': '三通',
    'cross': '十字接头',
    'reducer': '异径管',
    'nipple': '短管',
    'plug': '塞子、火花塞',
    'cap': '盖',
    'cover': '盖板',
    'housing': '壳体',
    'case': '箱体',
    'container': '容器',
    'tank': '油箱',
    'vessel': '容器',
    'pipe': '管道',
    'tube': '管子',
    'duct': '管道',
    'channel': '通道',
    'passage': '通道',
    'opening': '开口',
    'hole': '孔',
    'bore': '孔径',
    'slot': '槽',
    'groove': '沟槽',
    'recess': '凹槽',
    'cavity': '腔',
    'pocket': '凹坑',
    'chamber': '腔室',
    'compartment': '隔间',
    'space': '空间',
    'gap': '间隙',
    'clearance': '间隙',
    'tolerance': '公差',
    'fit': '配合',
    'interference': '过盈',
    'allowance': '余量',
    'margin': '余量',
    'excess': '过量',
    'shortage': '不足',
    'deficiency': '缺陷',
    'defect': '缺陷',
    'flaw': '瑕疵',
    'crack': '裂纹',
    'break': '断裂',
    'fracture': '断裂',
    'rupture': '破裂',
    'tear': '撕裂',
    'cut': '切口',
    'scratch': '划痕',
    'dent': '凹痕',
    'bend': '弯曲',
    'twist': '扭曲',
    'warp': '翘曲',
    'distortion': '变形',
    'deformation': '变形',
}

