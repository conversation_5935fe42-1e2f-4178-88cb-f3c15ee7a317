#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF Vocabulary Analyzer for HET Professional Terms
Extracts text from PDFs and analyzes vocabulary frequency
"""

import os
import re
from collections import Counter
from pathlib import Path
import json

try:
    import pdfplumber
except ImportError:
    print("Installing pdfplumber...")
    os.system("pip install pdfplumber")
    import pdfplumber

# Common English words to filter out (stop words)
STOP_WORDS = {
    'the', 'be', 'to', 'of', 'and', 'a', 'in', 'that', 'have', 'i', 'it', 'for', 'not', 'on', 'with',
    'he', 'as', 'you', 'do', 'at', 'this', 'but', 'his', 'by', 'from', 'they', 'we', 'say', 'her',
    'she', 'or', 'an', 'will', 'my', 'one', 'all', 'would', 'there', 'their', 'what', 'so', 'up',
    'out', 'if', 'about', 'who', 'get', 'which', 'go', 'me', 'when', 'make', 'can', 'like', 'time',
    'no', 'just', 'him', 'know', 'take', 'people', 'into', 'year', 'your', 'good', 'some', 'could',
    'them', 'see', 'other', 'than', 'then', 'now', 'look', 'only', 'come', 'its', 'over', 'think',
    'also', 'back', 'after', 'use', 'two', 'how', 'our', 'work', 'first', 'well', 'way', 'even',
    'new', 'want', 'because', 'any', 'these', 'give', 'day', 'most', 'us', 'is', 'was', 'are',
    'been', 'has', 'had', 'were', 'said', 'did', 'having', 'may', 'should', 'am', 'being', 'each',
    'such', 'during', 'here', 'where', 'both', 'few', 'more', 'very', 'through', 'between', 'under',
    'own', 'however', 'another', 'same', 'while', 'those', 'much', 'every', 'many', 'before', 'must',
    'too', 'does', 'part', 'once', 'least', 'made', 'over', 'still', 'since', 'per', 'thus', 'far',
    'yes', 'no', 'not', 'nor', 'yet', 'so', 'etc', 'eg', 'ie', 'via', 'vs', 'page', 'pages', 'fig',
    'figure', 'table', 'section', 'chapter', 'appendix', 'ref', 'references', 'note', 'notes'
}

def extract_text_from_pdf(pdf_path):
    """Extract text from a single PDF file"""
    text = ""
    try:
        with pdfplumber.open(pdf_path) as pdf:
            for page in pdf.pages:
                page_text = page.extract_text()
                if page_text:
                    text += page_text + "\n"
        print(f"✓ Extracted: {pdf_path.name}")
    except Exception as e:
        print(f"✗ Error extracting {pdf_path.name}: {e}")
    return text

def extract_all_pdfs(directory):
    """Extract text from all PDF files in directory"""
    pdf_files = list(Path(directory).glob("*.pdf"))
    print(f"\nFound {len(pdf_files)} PDF files")
    print("=" * 60)
    
    all_text = ""
    for pdf_file in pdf_files:
        text = extract_text_from_pdf(pdf_file)
        all_text += text
    
    # Save combined text
    output_file = Path(directory) / "ocred" / "all_extracted_text.txt"
    output_file.parent.mkdir(exist_ok=True)
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(all_text)
    
    print(f"\n✓ Saved combined text to: {output_file}")
    return all_text

def analyze_vocabulary(text):
    """Analyze vocabulary and return frequency counter"""
    # Convert to lowercase and extract words (only alphabetic, 3+ chars)
    words = re.findall(r'\b[a-zA-Z]{3,}\b', text.lower())
    
    # Filter out stop words and get professional terms
    professional_words = [w for w in words if w not in STOP_WORDS]
    
    # Count frequency
    word_freq = Counter(professional_words)
    
    return word_freq

def find_example_sentence(text, word, max_length=150):
    """Find an example sentence containing the word"""
    # Split into sentences
    sentences = re.split(r'[.!?]+', text)
    
    # Find sentences containing the word
    for sentence in sentences:
        if re.search(r'\b' + re.escape(word) + r'\b', sentence, re.IGNORECASE):
            sentence = sentence.strip()
            if 20 < len(sentence) < max_length:
                return sentence
    
    # If no good sentence found, return a shorter one
    for sentence in sentences:
        if re.search(r'\b' + re.escape(word) + r'\b', sentence, re.IGNORECASE):
            sentence = sentence.strip()
            if len(sentence) > 10:
                return sentence[:max_length] + "..." if len(sentence) > max_length else sentence
    
    return "No example found"

def main():
    """Main function"""
    print("=" * 60)
    print("PDF Vocabulary Analyzer for HET Professional Terms")
    print("=" * 60)
    
    # Extract text from all PDFs
    current_dir = Path(".")
    all_text = extract_all_pdfs(current_dir)
    
    print(f"\nTotal characters extracted: {len(all_text):,}")
    
    # Analyze vocabulary
    print("\nAnalyzing vocabulary frequency...")
    word_freq = analyze_vocabulary(all_text)
    
    # Get top 10
    top_10 = word_freq.most_common(10)
    
    print("\n" + "=" * 60)
    print("TOP 10 MOST FREQUENT HET PROFESSIONAL VOCABULARY")
    print("=" * 60)
    
    results = []
    for rank, (word, count) in enumerate(top_10, 1):
        example = find_example_sentence(all_text, word)
        results.append({
            'rank': rank,
            'word': word,
            'frequency': count,
            'example': example
        })
        
        print(f"\n{rank}. {word.upper()}")
        print(f"   Frequency: {count}")
        print(f"   Example: {example}")
    
    # Save results to JSON
    output_json = current_dir / "ocred" / "top_10_vocabulary.json"
    with open(output_json, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"\n✓ Results saved to: {output_json}")
    print("\n" + "=" * 60)

if __name__ == "__main__":
    main()

