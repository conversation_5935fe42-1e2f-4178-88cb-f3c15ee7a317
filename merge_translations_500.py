#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Merge existing translations into 500-word vocabulary
"""

import json
from pathlib import Path

# Load 500-word vocabulary
with open('ocred/vocabulary_500.json', encoding='utf-8') as f:
    vocab_500 = json.load(f)

# Load existing 200-word vocabulary with translations
with open('ocred/vocabulary_ultimate.json', encoding='utf-8') as f:
    vocab_200 = json.load(f)

# Create translation dictionary from 200-word data
translation_dict = {}
for item in vocab_200:
    translation_dict[item['word'].lower()] = {
        'translation': item['translation'],
        'example': item['example']
    }

# Extended translations dictionary (from previous work)
EXTENDED_TRANSLATIONS = {
    'brake': '制动器、刹车',
    'pressure': '压力',
    'valve': '阀门',
    'air': '空气',
    'system': '系统',
    'trailer': '拖车、挂车',
    'used': '使用的',
    'hydraulic': '液压的',
    'circuit': '电路',
    'spring': '弹簧',
    'vehicle': '车辆',
    'component': '部件、组件',
    'brakes': '制动器（复数）',
    'equipment': '设备',
    'wheel': '车轮',
    'control': '控制',
    'service': '维修、保养',
    'chamber': '腔室',
    'parking': '停车、驻车',
    'drum': '鼓',
    'line': '管路、线路',
    'reservoir': '储液罐',
    'hose': '软管',
    'assembly': '总成',
    'axle': '车轴',
    'psi': '磅/平方英寸（压力单位）',
    'application': '应用',
    'release': '释放',
    'force': '力',
    'fluid': '液体、流体',
    'cylinder': '气缸、液压缸',
    'piston': '活塞',
    'seal': '密封件',
    'bearing': '轴承',
    'shaft': '轴',
    'housing': '壳体',
    'bolt': '螺栓',
    'nut': '螺母',
    'washer': '垫圈',
    'gasket': '垫片',
    'filter': '滤清器',
    'pump': '泵',
    'motor': '马达、电机',
    'engine': '发动机',
    'transmission': '变速器',
    'clutch': '离合器',
    'gear': '齿轮',
    'differential': '差速器',
    'suspension': '悬挂',
    'shock': '减震器',
    'strut': '支柱',
    'bushing': '衬套',
    'joint': '接头',
    'rod': '杆',
    'arm': '臂',
    'link': '连杆',
    'pin': '销',
    'clip': '卡子',
    'clamp': '夹具',
    'bracket': '支架',
    'mount': '安装座',
    'plate': '板',
    'cover': '盖',
    'cap': '帽',
    'plug': '塞子',
    'fitting': '接头',
    'connector': '连接器',
    'adapter': '适配器',
    'coupling': '联轴器',
    'union': '管接头',
    'elbow': '弯头',
    'tee': '三通',
    'reducer': '异径管',
    'nipple': '短管',
    'tube': '管',
    'pipe': '管道',
    'duct': '管道',
    'cable': '电缆',
    'wire': '电线',
    'harness': '线束',
    'switch': '开关',
    'relay': '继电器',
    'fuse': '保险丝',
    'battery': '电池',
    'alternator': '交流发电机',
    'starter': '起动机',
    'solenoid': '电磁阀',
    'sensor': '传感器',
    'gauge': '仪表',
    'indicator': '指示器',
    'warning': '警告',
    'signal': '信号',
    'light': '灯',
    'lamp': '灯',
    'bulb': '灯泡',
    'lens': '透镜',
    'reflector': '反射器',
    'mirror': '镜子',
    'glass': '玻璃',
    'windshield': '挡风玻璃',
    'wiper': '雨刷',
    'blade': '刮片',
    'washer': '清洗器',
    'nozzle': '喷嘴',
    'tank': '油箱',
    'fuel': '燃料',
    'oil': '机油',
    'grease': '润滑脂',
    'lubricant': '润滑剂',
    'coolant': '冷却液',
    'antifreeze': '防冻液',
    'refrigerant': '制冷剂',
    'exhaust': '排气',
    'muffler': '消音器',
    'catalytic': '催化的',
    'converter': '转换器',
    'manifold': '歧管',
    'intake': '进气',
    'throttle': '节气门',
    'carburetor': '化油器',
    'injector': '喷油器',
    'turbo': '涡轮',
    'supercharger': '增压器',
    'intercooler': '中冷器',
    'radiator': '散热器',
    'fan': '风扇',
    'belt': '皮带',
    'chain': '链条',
    'sprocket': '链轮',
    'pulley': '皮带轮',
    'tensioner': '张紧器',
    'idler': '惰轮',
    'damper': '减振器',
    'balancer': '平衡器',
    'flywheel': '飞轮',
    'crankshaft': '曲轴',
    'camshaft': '凸轮轴',
    'timing': '正时',
    'valve': '气门',
    'lifter': '挺杆',
    'rocker': '摇臂',
    'pushrod': '推杆',
}

# Merge translations
updated_count = 0
for item in vocab_500:
    word_lower = item['word'].lower()
    
    # First try to get from 200-word data
    if word_lower in translation_dict:
        item['translation'] = translation_dict[word_lower]['translation']
        item['example'] = translation_dict[word_lower]['example']
        updated_count += 1
    # Then try extended translations
    elif word_lower in EXTENDED_TRANSLATIONS:
        item['translation'] = EXTENDED_TRANSLATIONS[word_lower]
        updated_count += 1

# Save updated vocabulary
with open('ocred/vocabulary_500_merged.json', 'w', encoding='utf-8') as f:
    json.dump(vocab_500, f, ensure_ascii=False, indent=2)

print(f"✓ Merged translations")
print(f"  Total words: 500")
print(f"  Translated: {updated_count}")
print(f"  Remaining: {500 - updated_count}")
print(f"  Coverage: {updated_count/500*100:.1f}%")
print(f"\n✓ Saved to: ocred/vocabulary_500_merged.json")

