#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import json

# Load the vocabulary data
with open('ocred/comprehensive_vocabulary.json', encoding='utf-8') as f:
    data = json.load(f)

# Find words that need translation
missing = [item for item in data if '需要人工翻译' in item['translation']]

print(f"需要翻译的词汇数量: {len(missing)}\n")
print("=" * 60)

for item in missing[:80]:
    print(f"{item['rank']}. {item['word']} (频率: {item['frequency']})")

