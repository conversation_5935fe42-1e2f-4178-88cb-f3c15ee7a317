import pdfplumber
import re
from collections import Counter
from pathlib import Path
import json

# Extract text from all PDFs
text_data = []
pdf_files = sorted(Path('.').glob('*.pdf'))

print(f"Found {len(pdf_files)} PDF files")

for pdf_file in pdf_files:
    try:
        with pdfplumber.open(pdf_file) as pdf:
            for page in pdf.pages:
                text = page.extract_text()
                if text:
                    text_data.append(text)
        print(f"Extracted: {pdf_file.name}")
    except Exception as e:
        print(f"Error processing {pdf_file.name}: {e}")

# Combine all text
all_text = ' '.join(text_data)

# Tokenize: convert to lowercase, remove punctuation, split into words
words = re.findall(r'\b[a-z]+\b', all_text.lower())

# Count word frequencies
word_freq = Counter(words)

# Get top 500 words
top_500 = word_freq.most_common(500)

# Save results
results = []
for word, count in top_500:
    results.append({
        'word': word,
        'frequency': count,
        'chinese': '',
        'example': ''
    })

with open('top_500_words.json', 'w', encoding='utf-8') as f:
    json.dump(results, f, ensure_ascii=False, indent=2)

print(f"\nExtracted {len(words)} total words")
print(f"Found {len(word_freq)} unique words")
print(f"Top 10 words:")
for word, count in top_500[:10]:
    print(f"  {word}: {count}")

print("\nResults saved to top_500_words.json")
