#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Final HET Vocabulary Document Generator
Creates comprehensive vocabulary document with all translations
"""

import json
from pathlib import Path
from extended_translations import EXTENDED_TRANSLATIONS

# Load existing vocabulary data
with open('ocred/comprehensive_vocabulary.json', encoding='utf-8') as f:
    vocab_data = json.load(f)

# Base translations from original script
BASE_TRANSLATIONS = {
    'brake': '制动器、刹车',
    'pressure': '压力',
    'valve': '阀门',
    'air': '空气',
    'safety': '安全',
    'equipment': '设备',
    'vehicle': '车辆',
    'system': '系统',
    'hydraulic': '液压的',
    'cylinder': '气缸、圆柱体',
    'engine': '发动机',
    'fuel': '燃料',
    'oil': '油、机油',
    'transmission': '变速器、传动系统',
    'electrical': '电气的',
    'circuit': '电路',
    'battery': '电池',
    'voltage': '电压',
    'current': '电流',
    'resistance': '电阻',
    'wire': '电线',
    'connector': '连接器',
    'sensor': '传感器',
    'control': '控制',
    'module': '模块',
    'diagnostic': '诊断的',
    'repair': '维修',
    'maintenance': '维护、保养',
    'inspection': '检查',
    'service': '服务、维修',
    'tool': '工具',
    'torque': '扭矩',
    'specification': '规格',
    'manual': '手册',
    'procedure': '程序',
    'component': '部件',
}

# Merge all translations
ALL_TRANSLATIONS = {**BASE_TRANSLATIONS, **EXTENDED_TRANSLATIONS}

# Update translations in vocabulary data
updated_count = 0
for item in vocab_data:
    word = item['word'].lower()
    if word in ALL_TRANSLATIONS:
        item['translation'] = ALL_TRANSLATIONS[word]
        updated_count += 1

print(f"✓ Updated {updated_count} translations")

# Save updated JSON
with open('ocred/comprehensive_vocabulary_final.json', 'w', encoding='utf-8') as f:
    json.dump(vocab_data, f, indent=2, ensure_ascii=False)

print("✓ Saved updated JSON")

# Generate final markdown document
md_content = """# HET专业词汇大全
## Higher Education and Training Professional Vocabulary

**📅 文档生成时间：** 2026年2月17日  
**📚 数据来源：** 39个PDF文档分析  
**📊 总提取字符数：** 2,177,809  
**📖 词汇总数：** 200个高频专业词汇

---

## 📋 文档说明

本文档通过分析39个HET（Higher Education and Training）相关PDF文档，提取了出现频率最高的200个专业词汇。

### 文档特点：
- ✅ 按出现频率排序
- ✅ 提供中文翻译
- ✅ 包含实际例句
- ✅ 标注出现次数

### 主要涵盖领域：
- 🚗 汽车制动系统 (Automotive Brake Systems)
- 🔧 液压系统 (Hydraulic Systems)
- ⚡ 电气系统 (Electrical Systems)
- 🛠️ 维修保养 (Maintenance & Repair)
- ⚠️ 职业安全 (Occupational Safety)
- 🔩 机械部件 (Mechanical Components)

---

## 📊 词汇列表

"""

# Add all vocabulary entries
for item in vocab_data:
    rank = item['rank']
    word = item['word']
    freq = item['frequency']
    translation = item['translation']
    example = item['example'].replace('\n', ' ')
    
    md_content += f"""
### {rank}. **{word.upper()}**

**中文：** {translation}  
**频率：** {freq}次  
**例句：** {example}

---
"""

# Add footer with statistics and categories
md_content += """
## 📈 词汇统计分析

### 按技术领域分类：

#### 🚗 制动系统相关 (Brake Systems)
- brake (制动器) - 2615次
- brakes (制动器复数) - 677次
- braking (制动) - 285次
- drum (制动鼓) - 312次
- parking (停车制动) - 222次

#### 💧 液压系统相关 (Hydraulic Systems)
- hydraulic (液压的) - 915次
- pressure (压力) - 1523次
- valve (阀门) - 1455次
- reservoir (储液罐) - 375次
- pump (泵) - 相关词汇

#### ⚙️ 机械部件相关 (Mechanical Components)
- components (部件) - 721次
- assembly (总成) - 相关词汇
- axle (车轴) - 402次
- frame (车架) - 473次
- plate (板) - 239次

#### 🔌 电气系统相关 (Electrical Systems)
- circuit (电路) - 相关词汇
- electrical (电气的) - 相关词汇
- signal (信号) - 237次
- conductor (导体) - 214次

---

## 💡 学习建议

1. **优先学习高频词汇**：前50个词汇占据了文档中大部分专业内容
2. **结合例句理解**：每个词汇都提供了实际使用场景
3. **注意词汇搭配**：观察词汇在技术文档中的常见搭配
4. **分类记忆**：按技术领域分类记忆效果更好

---

## 📝 使用说明

### 适用人群：
- 汽车维修技术人员
- 重型设备操作员
- 职业培训学员
- 技术文档翻译人员

### 使用方法：
1. 按频率从高到低学习
2. 重点掌握前100个高频词汇
3. 结合实际工作场景应用
4. 定期复习巩固

---

## 🔍 数据来源

本文档基于以下39个PDF文件分析生成：
- 190101系列：8个文件
- 190102系列：8个文件  
- 190103系列：9个文件
- 190104系列：7个文件
- 190105系列：7个文件

总计提取文本：2,177,809字符

---

## ⚠️ 免责声明

1. 本文档仅供学习参考使用
2. 部分专业术语可能有多种翻译，请根据具体语境理解
3. 建议结合专业教材和实际工作经验使用

---

**文档生成工具：** Python + pdfplumber  
**分析方法：** 词频统计 + 自然语言处理  
**最后更新：** 2026-02-17

---

*© 2026 HET Professional Vocabulary Database*
"""

# Save final markdown document
output_file = Path("HET专业词汇大全_完整版.md")
with open(output_file, 'w', encoding='utf-8') as f:
    f.write(md_content)

print(f"✓ Final document saved to: {output_file}")
print(f"✓ Total vocabulary entries: {len(vocab_data)}")
print(f"✓ Document size: {len(md_content):,} characters")

# Check how many still need translation
still_missing = [item for item in vocab_data if '需要人工翻译' in item['translation']]
print(f"✓ Translations completed: {len(vocab_data) - len(still_missing)}/{len(vocab_data)}")
print(f"✓ Still need translation: {len(still_missing)}")

print("\n" + "=" * 70)
print("✅ FINAL DOCUMENT GENERATION COMPLETE!")
print("=" * 70)

