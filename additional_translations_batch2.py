#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Additional translations - Batch 2
More specialized HET vocabulary
"""

BATCH2_TRANSLATIONS = {
    # More technical terms
    'pushrod': '推杆',
    'diaphragm': '隔膜',
    'piston': '活塞',
    'cylinder': '气缸、圆柱体',
    'chamber': '腔室',
    'actuator': '执行器',
    'solenoid': '电磁阀',
    'relay': '继电器',
    'compressor': '压缩机',
    'accumulator': '蓄能器',
    'regulator': '调节器',
    'governor': '调速器',
    'modulator': '调制器',
    'proportioning': '比例的',
    'metering': '计量',
    'restrictor': '限流器',
    'orifice': '孔口',
    'nozzle': '喷嘴',
    'injector': '喷油器',
    'carburetor': '化油器',
    'throttle': '节气门',
    'choke': '阻风门',
    'manifold': '歧管',
    'plenum': '增压室',
    'intake': '进气',
    'exhaust': '排气',
    'muffler': '消音器',
    'catalytic': '催化的',
    'converter': '转换器',
    'turbocharger': '涡轮增压器',
    'supercharger': '机械增压器',
    'intercooler': '中冷器',
    'radiator': '散热器',
    'condenser': '冷凝器',
    'evaporator': '蒸发器',
    'thermostat': '恒温器',
    'coolant': '冷却液',
    'antifreeze': '防冻液',
    'lubricant': '润滑剂',
    'grease': '润滑脂',
    'sealant': '密封剂',
    'adhesive': '粘合剂',
    'gasket': '垫片',
    'seal': '密封件',
    'bearing': '轴承',
    'bushing': '衬套',
    'shaft': '轴',
    'axle': '车轴',
    'spindle': '主轴',
    'crankshaft': '曲轴',
    'camshaft': '凸轮轴',
    'driveshaft': '传动轴',
    'propeller': '传动轴',
    'differential': '差速器',
    'transmission': '变速器、传动系统',
    'gearbox': '变速箱',
    'clutch': '离合器',
    'flywheel': '飞轮',
    'torque': '扭矩',
    'gear': '齿轮',
    'sprocket': '链轮',
    'pulley': '滑轮',
    'sheave': '滑轮',
    'belt': '皮带',
    'chain': '链条',
    'cable': '电缆',
    'rope': '绳索',
    'strap': '带子',
    'sling': '吊索',
    'hook': '钩子',
    'shackle': '卸扣',
    'clevis': '钩头',
    'eyebolt': '吊环螺栓',
    'turnbuckle': '花兰螺丝',
    'anchor': '锚固件',
    'bracket': '支架',
    'mount': '安装座',
    'hanger': '吊架',
    'support': '支撑',
    'brace': '支撑',
    'strut': '支柱',
    'beam': '梁',
    'column': '柱',
    'truss': '桁架',
    'chassis': '底盘',
    'frame': '框架、车架',
    'subframe': '副车架',
    'crossmember': '横梁',
    'rail': '纵梁',
    'sill': '门槛梁',
    'panel': '面板',
    'skin': '蒙皮',
    'fender': '挡泥板',
    'bumper': '保险杠',
    'hood': '发动机罩',
    'trunk': '行李箱',
    'door': '门',
    'window': '窗',
    'windshield': '挡风玻璃',
    'mirror': '镜子',
    'wiper': '雨刷',
    'headlight': '前照灯',
    'taillight': '尾灯',
    'indicator': '指示器',
    'horn': '喇叭',
    'siren': '警报器',
    'alarm': '警报',
    'gauge': '仪表',
    'meter': '仪表',
    'tachometer': '转速表',
    'speedometer': '速度表',
    'odometer': '里程表',
    'ammeter': '电流表',
    'voltmeter': '电压表',
    'ohmmeter': '欧姆表',
    'multimeter': '万用表',
    'oscilloscope': '示波器',
    'scanner': '扫描仪',
    'tester': '测试仪',
    'analyzer': '分析仪',
    'detector': '检测器',
    'probe': '探头',
    'electrode': '电极',
    'terminal': '端子',
    'connector': '连接器',
    'harness': '线束',
    'wire': '电线',
    'cable': '电缆',
    'fuse': '保险丝',
    'breaker': '断路器',
    'switch': '开关',
    'button': '按钮',
    'knob': '旋钮',
    'lever': '杠杆',
    'pedal': '踏板',
    'handle': '手柄',
    'grip': '握把',
    'steering': '转向系统',
    'wheel': '车轮',
    'tire': '轮胎',
    'rim': '轮辋',
    'hub': '轮毂',
    'spoke': '辐条',
    'suspension': '悬挂系统',
    'shock': '减震器',
    'absorber': '吸收器',
    'damper': '阻尼器',
    'strut': '支柱',
    'coil': '线圈',
    'leaf': '钢板',
}

# Save to file for later use
if __name__ == "__main__":
    import json
    with open('batch2_translations.json', 'w', encoding='utf-8') as f:
        json.dump(BATCH2_TRANSLATIONS, f, indent=2, ensure_ascii=False)
    print(f"✓ Saved {len(BATCH2_TRANSLATIONS)} additional translations")

