#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Expand vocabulary analysis to 500 words
"""

import json
import re
from pathlib import Path
from collections import Counter

# Load the extracted text
text_file = Path('ocred/all_extracted_text.txt')
with open(text_file, 'r', encoding='utf-8') as f:
    text = f.read()

# Expanded stop words list
STOP_WORDS = {
    'the', 'be', 'to', 'of', 'and', 'a', 'in', 'that', 'have', 'i',
    'it', 'for', 'not', 'on', 'with', 'he', 'as', 'you', 'do', 'at',
    'this', 'but', 'his', 'by', 'from', 'they', 'we', 'say', 'her', 'she',
    'or', 'an', 'will', 'my', 'one', 'all', 'would', 'there', 'their', 'what',
    'so', 'up', 'out', 'if', 'about', 'who', 'get', 'which', 'go', 'me',
    'when', 'make', 'can', 'like', 'time', 'no', 'just', 'him', 'know', 'take',
    'people', 'into', 'year', 'your', 'good', 'some', 'could', 'them', 'see', 'other',
    'than', 'then', 'now', 'look', 'only', 'come', 'its', 'over', 'think', 'also',
    'back', 'after', 'use', 'two', 'how', 'our', 'work', 'first', 'well', 'way',
    'even', 'new', 'want', 'because', 'any', 'these', 'give', 'day', 'most', 'us',
    'is', 'was', 'are', 'been', 'has', 'had', 'were', 'said', 'did', 'having',
    'may', 'should', 'must', 'shall', 'being', 'does', 'am',
    # Alberta-specific stop words
    'alberta', 'province', 'queen', 'majesty', 'crown', 'government', 'act',
    'regulation', 'section', 'subsection', 'paragraph', 'clause', 'minister',
    'director', 'inspector', 'person', 'means', 'include', 'includes',
    'right', 'left', 'front', 'rear', 'side', 'each', 'every', 'such',
    'following', 'accordance', 'respect', 'pursuant', 'prescribed', 'required',
    'page', 'figure', 'table', 'note', 'see', 'refer', 'reference',
    'chapter', 'part', 'appendix', 'schedule',
    # Common non-technical words
    'made', 'between', 'during', 'without', 'before', 'under', 'around',
    'however', 'therefore', 'thus', 'where', 'whether', 'while', 'within',
    'through', 'upon', 'against', 'among', 'both', 'either', 'neither',
    'same', 'different', 'another', 'next', 'last', 'many', 'much', 'more',
    'less', 'few', 'several', 'various', 'certain', 'particular', 'general',
    'specific', 'appropriate', 'applicable', 'relevant', 'related',
}

# Extract words (only alphabetic, 3+ characters)
words = re.findall(r'\b[a-zA-Z]{3,}\b', text.lower())

# Filter out stop words
filtered_words = [w for w in words if w not in STOP_WORDS]

# Count frequency
word_freq = Counter(filtered_words)

# Get top 500
top_500 = word_freq.most_common(500)

print(f"Total words found: {len(words):,}")
print(f"After filtering: {len(filtered_words):,}")
print(f"Unique words: {len(word_freq):,}")
print(f"\nTop 500 words extracted")

# Find example sentences for each word (optimized - use first 100k chars only for speed)
text_sample = text[:100000]
vocabulary_data = []

for rank, (word, frequency) in enumerate(top_500, 1):
    # Find first occurrence in sample text for example sentence
    pattern = re.compile(r'([^.!?]*\b' + re.escape(word) + r'\b[^.!?]*[.!?])', re.IGNORECASE)
    match = pattern.search(text_sample)

    if match:
        example = match.group(1).strip()
        # Limit example length
        if len(example) > 150:
            example = example[:147] + "..."
    else:
        example = f"Technical term used in HET contexts."

    vocabulary_data.append({
        'rank': rank,
        'word': word,
        'frequency': frequency,
        'translation': '需要人工翻译',
        'example': example
    })

    if rank % 100 == 0:
        print(f"Processed {rank}/500 words...")

# Save to JSON
output_file = Path('ocred/vocabulary_500.json')
with open(output_file, 'w', encoding='utf-8') as f:
    json.dump(vocabulary_data, f, ensure_ascii=False, indent=2)

print(f"\n✓ Saved 500 vocabulary words to: {output_file}")

# Calculate statistics
total_freq = sum(item['frequency'] for item in vocabulary_data)
top10_freq = sum(item['frequency'] for item in vocabulary_data[:10])
top20_freq = sum(item['frequency'] for item in vocabulary_data[:20])
top50_freq = sum(item['frequency'] for item in vocabulary_data[:50])
top100_freq = sum(item['frequency'] for item in vocabulary_data[:100])
top200_freq = sum(item['frequency'] for item in vocabulary_data[:200])

print(f"\n{'='*70}")
print("FREQUENCY DISTRIBUTION ANALYSIS")
print(f"{'='*70}")
print(f"Total frequency (500 words): {total_freq:,}")
print(f"Top 10 coverage: {top10_freq/total_freq*100:.1f}%")
print(f"Top 20 coverage: {top20_freq/total_freq*100:.1f}%")
print(f"Top 50 coverage: {top50_freq/total_freq*100:.1f}%")
print(f"Top 100 coverage: {top100_freq/total_freq*100:.1f}%")
print(f"Top 200 coverage: {top200_freq/total_freq*100:.1f}%")
print(f"Top 500 coverage: 100.0%")
print(f"{'='*70}")

# Show top 20
print("\nTop 20 words:")
for item in vocabulary_data[:20]:
    print(f"{item['rank']:3d}. {item['word']:20s} - {item['frequency']:5d} times")

print(f"\n✓ Vocabulary expansion to 500 words complete!")

