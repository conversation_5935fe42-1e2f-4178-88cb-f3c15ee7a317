#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Generate categorized technical dictionary
"""

import json
from pathlib import Path
from collections import defaultdict

# Load vocabulary data
with open('ocred/vocabulary_ultimate.json', encoding='utf-8') as f:
    vocab_data = json.load(f)

# Define categories and keywords
CATEGORIES = {
    '制动系统 (Brake Systems)': ['brake', 'braking', 'drum', 'parking', 'stop'],
    '液压系统 (Hydraulic Systems)': ['hydraulic', 'pressure', 'valve', 'pump', 'reservoir', 'fluid', 'psi', 'kpa'],
    '电气系统 (Electrical Systems)': ['electrical', 'circuit', 'wire', 'battery', 'voltage', 'current', 'signal', 'conductor'],
    '发动机系统 (Engine Systems)': ['engine', 'fuel', 'oil', 'cylinder', 'piston', 'combustion'],
    '传动系统 (Transmission Systems)': ['transmission', 'gear', 'clutch', 'drive', 'axle', 'differential'],
    '悬挂转向系统 (Suspension & Steering)': ['suspension', 'steering', 'wheel', 'spring', 'shock'],
    '机械部件 (Mechanical Components)': ['component', 'assembly', 'plate', 'chamber', 'unit', 'frame'],
    '工具设备 (Tools & Equipment)': ['tool', 'equipment', 'jack', 'wrench', 'socket'],
    '安全防护 (Safety & Protection)': ['safety', 'protection', 'protective', 'hazard', 'emergency'],
    '测量检测 (Measurement & Testing)': ['test', 'check', 'measure', 'gauge', 'meter', 'reading'],
    '材料物质 (Materials)': ['material', 'steel', 'metal', 'rubber', 'plastic'],
    '操作维修 (Operation & Maintenance)': ['operation', 'service', 'repair', 'maintenance', 'inspection'],
    '车辆类型 (Vehicle Types)': ['vehicle', 'trailer', 'tractor', 'truck'],
    '位置方向 (Position & Direction)': ['position', 'direction', 'side', 'end', 'located'],
    '尺寸规格 (Size & Specification)': ['size', 'length', 'diameter', 'inch', 'specification'],
}

# Categorize vocabulary
categorized = defaultdict(list)

for item in vocab_data:
    word = item['word'].lower()
    assigned = False
    
    for category, keywords in CATEGORIES.items():
        if any(kw in word for kw in keywords):
            categorized[category].append(item)
            assigned = True
            break
    
    if not assigned:
        categorized['其他 (Others)'].append(item)

# Generate categorized dictionary
dict_md = """# 📚 HET专业词汇分类词典
## Categorized Technical Dictionary

本词典将200个HET专业词汇按技术领域分类，便于系统学习和快速查找。

---

## 📊 分类统计

"""

# Add statistics
for category in sorted(categorized.keys()):
    count = len(categorized[category])
    dict_md += f"- **{category}**: {count}个词汇\n"

dict_md += "\n---\n\n"

# Add each category
for category in sorted(categorized.keys()):
    items = categorized[category]
    dict_md += f"""
## 🔧 {category}

共{len(items)}个词汇

| # | 英文 | 中文 | 频率 | 例句 |
|---|------|------|------|------|
"""
    
    for item in sorted(items, key=lambda x: x['frequency'], reverse=True):
        example_short = item['example'][:80].replace('\n', ' ') + '...' if len(item['example']) > 80 else item['example'].replace('\n', ' ')
        dict_md += f"| {item['rank']} | {item['word']} | {item['translation']} | {item['frequency']} | {example_short} |\n"
    
    dict_md += "\n---\n"

dict_md += """
## 📖 使用指南

### 如何使用本词典

1. **按领域学习**：根据你的工作领域，优先学习相关分类的词汇
2. **系统记忆**：同一类别的词汇一起学习，便于建立知识体系
3. **快速查找**：需要某个领域的词汇时，直接查看对应分类
4. **交叉学习**：注意某些词汇可能涉及多个领域

### 学习建议

- **制动系统**：汽车维修技师必学
- **液压系统**：重型设备操作员重点
- **电气系统**：电气维修人员必备
- **安全防护**：所有从业人员必须掌握

---

*© 2026 HET Categorized Technical Dictionary*
"""

# Save categorized dictionary
dict_file = Path("HET专业词汇分类词典.md")
with open(dict_file, 'w', encoding='utf-8') as f:
    f.write(dict_md)

print(f"✓ Generated categorized dictionary: {dict_file}")
print(f"✓ Total categories: {len(categorized)}")
for category, items in sorted(categorized.items()):
    print(f"  - {category}: {len(items)} words")

# Generate quick reference guide
quick_ref = """# 🚀 HET词汇速查手册
## Quick Reference Guide

本手册提供最常用的HET专业词汇快速查询。

---

## ⚡ 超高频词汇 (Top 20)

这20个词汇占据文档内容的50%以上，必须优先掌握！

| 排名 | 英文 | 中文 | 频率 |
|------|------|------|------|
"""

for item in vocab_data[:20]:
    quick_ref += f"| {item['rank']} | **{item['word'].upper()}** | {item['translation']} | {item['frequency']} |\n"

quick_ref += """
---

## 🔥 高频词汇 (21-50)

"""

for item in vocab_data[20:50]:
    quick_ref += f"**{item['rank']}.** {item['word']} = {item['translation']} ({item['frequency']}次)  \n"

quick_ref += """
---

## 📌 常用短语搭配

### 制动系统相关
- brake system - 制动系统
- brake fluid - 制动液
- brake pressure - 制动压力
- brake valve - 制动阀
- parking brake - 驻车制动

### 液压系统相关
- hydraulic system - 液压系统
- hydraulic pressure - 液压压力
- hydraulic fluid - 液压油
- pressure valve - 压力阀
- relief valve - 溢流阀

### 电气系统相关
- electrical circuit - 电路
- circuit breaker - 断路器
- electrical system - 电气系统
- wire harness - 线束
- electrical connector - 电气连接器

### 安全相关
- safety equipment - 安全设备
- protective equipment - 防护设备
- safety valve - 安全阀
- emergency brake - 紧急制动
- safety inspection - 安全检查

---

## 🔢 数字与单位

### 压力单位
- PSI (Pounds per Square Inch) - 磅/平方英寸
- KPA (Kilopascal) - 千帕
- 1 PSI = 6.895 KPA

### 长度单位
- inch - 英寸
- foot - 英尺
- 1 inch = 2.54 cm
- 1 foot = 30.48 cm

### 扭矩单位
- lb-ft (pound-foot) - 磅英尺
- N·m (Newton-meter) - 牛顿米
- 1 lb-ft = 1.356 N·m

---

## 🎯 记忆口诀

### 制动系统三要素
**B-P-V**: Brake (制动器) - Pressure (压力) - Valve (阀门)

### 液压系统四部件
**P-V-R-F**: Pump (泵) - Valve (阀) - Reservoir (油箱) - Fluid (液体)

### 安全检查五步骤
**I-C-T-R-D**: Inspect (检查) - Check (核对) - Test (测试) - Repair (维修) - Document (记录)

---

## 📱 移动学习建议

1. **每天5分钟**：利用碎片时间复习10个词汇
2. **实物对照**：看到实物时回忆英文单词
3. **语音练习**：大声朗读词汇和例句
4. **笔记记录**：遇到不熟悉的词汇立即记录

---

*© 2026 HET Quick Reference Guide*
"""

# Save quick reference
quick_ref_file = Path("HET词汇速查手册.md")
with open(quick_ref_file, 'w', encoding='utf-8') as f:
    f.write(quick_ref)

print(f"✓ Generated quick reference: {quick_ref_file}")

print("\n" + "="*70)
print("✅ CATEGORIZED DICTIONARY AND QUICK REFERENCE GENERATED!")
print("="*70)

