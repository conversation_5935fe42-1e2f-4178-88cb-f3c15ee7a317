#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Comprehensive HET Vocabulary Analyzer
Extracts and analyzes top professional vocabulary with Chinese translations
"""

import json
import re
from collections import Counter
from pathlib import Path

# HET专业词汇中文翻译词典
VOCABULARY_TRANSLATIONS = {
    'brake': '制动器、刹车',
    'pressure': '压力',
    'valve': '阀门',
    'air': '空气',
    'safety': '安全',
    'equipment': '设备',
    'vehicle': '车辆',
    'system': '系统',
    'hydraulic': '液压的',
    'cylinder': '气缸、圆柱体',
    'engine': '发动机',
    'fuel': '燃料',
    'oil': '油、机油',
    'transmission': '变速器、传动系统',
    'electrical': '电气的',
    'circuit': '电路',
    'battery': '电池',
    'voltage': '电压',
    'current': '电流',
    'resistance': '电阻',
    'wire': '电线',
    'connector': '连接器',
    'sensor': '传感器',
    'control': '控制',
    'module': '模块',
    'diagnostic': '诊断的',
    'repair': '维修',
    'maintenance': '维护、保养',
    'inspection': '检查',
    'service': '服务、维修',
    'tool': '工具',
    'torque': '扭矩',
    'specification': '规格',
    'manual': '手册',
    'procedure': '程序',
    'component': '部件',
    'assembly': '组装',
    'disassembly': '拆卸',
    'installation': '安装',
    'removal': '拆除',
    'replacement': '更换',
    'adjustment': '调整',
    'calibration': '校准',
    'measurement': '测量',
    'clearance': '间隙',
    'tolerance': '公差',
    'wear': '磨损',
    'damage': '损坏',
    'corrosion': '腐蚀',
    'leak': '泄漏',
    'fluid': '液体',
    'coolant': '冷却液',
    'lubricant': '润滑剂',
    'gasket': '垫片',
    'seal': '密封件',
    'bearing': '轴承',
    'shaft': '轴',
    'gear': '齿轮',
    'clutch': '离合器',
    'suspension': '悬挂系统',
    'steering': '转向系统',
    'wheel': '车轮',
    'tire': '轮胎',
    'alignment': '对齐、定位',
    'exhaust': '排气',
    'emission': '排放',
    'filter': '过滤器',
    'pump': '泵',
    'compressor': '压缩机',
    'condenser': '冷凝器',
    'evaporator': '蒸发器',
    'radiator': '散热器',
    'thermostat': '恒温器',
    'hose': '软管',
    'clamp': '夹具',
    'bolt': '螺栓',
    'nut': '螺母',
    'washer': '垫圈',
    'screw': '螺钉',
    'fastener': '紧固件',
    'spring': '弹簧',
    'piston': '活塞',
    'crankshaft': '曲轴',
    'camshaft': '凸轮轴',
    'timing': '正时',
    'ignition': '点火',
    'spark': '火花',
    'plug': '插头、火花塞',
    'coil': '线圈',
    'distributor': '分电器',
    'alternator': '交流发电机',
    'starter': '起动机',
    'relay': '继电器',
    'fuse': '保险丝',
    'switch': '开关',
    'solenoid': '电磁阀',
    'actuator': '执行器',
    'throttle': '节气门',
    'injector': '喷油器',
    'manifold': '歧管',
    'intake': '进气',
    'compression': '压缩',
    'combustion': '燃烧',
    'power': '功率',
    'performance': '性能',
    'efficiency': '效率',
    'temperature': '温度',
    'gauge': '仪表',
    'indicator': '指示器',
    'warning': '警告',
    'malfunction': '故障',
    'code': '代码',
    'scanner': '扫描仪',
    'tester': '测试仪',
    'meter': '仪表',
    'multimeter': '万用表',
    'oscilloscope': '示波器',
    'wrench': '扳手',
    'socket': '套筒',
    'ratchet': '棘轮',
    'pliers': '钳子',
    'screwdriver': '螺丝刀',
    'hammer': '锤子',
    'chisel': '凿子',
    'punch': '冲子',
    'drill': '钻头、钻孔',
    'grinder': '研磨机',
    'welder': '焊机',
    'jack': '千斤顶',
    'lift': '升降机',
    'stand': '支架',
    'hoist': '起重机',
    'crane': '吊车',
    'chain': '链条',
    'cable': '电缆',
    'rope': '绳索',
    'strap': '带子',
    'sling': '吊索',
    'hook': '钩子',
    'pulley': '滑轮',
    'winch': '绞车',
    'protection': '保护',
    'hazard': '危险',
    'risk': '风险',
    'accident': '事故',
    'injury': '伤害',
    'emergency': '紧急情况',
    'fire': '火灾',
    'extinguisher': '灭火器',
    'ventilation': '通风',
    'exposure': '暴露',
    'chemical': '化学品',
    'toxic': '有毒的',
    'flammable': '易燃的',
    'explosive': '爆炸性的',
    'corrosive': '腐蚀性的',
}

def load_extracted_text():
    """Load the previously extracted text"""
    text_file = Path("ocred/all_extracted_text.txt")
    if not text_file.exists():
        print("Error: Extracted text file not found!")
        return ""

    with open(text_file, 'r', encoding='utf-8') as f:
        return f.read()

# Extended stop words list
STOP_WORDS = {
    'the', 'be', 'to', 'of', 'and', 'a', 'in', 'that', 'have', 'i', 'it', 'for', 'not', 'on', 'with',
    'he', 'as', 'you', 'do', 'at', 'this', 'but', 'his', 'by', 'from', 'they', 'we', 'say', 'her',
    'she', 'or', 'an', 'will', 'my', 'one', 'all', 'would', 'there', 'their', 'what', 'so', 'up',
    'out', 'if', 'about', 'who', 'get', 'which', 'go', 'me', 'when', 'make', 'can', 'like', 'time',
    'no', 'just', 'him', 'know', 'take', 'people', 'into', 'year', 'your', 'good', 'some', 'could',
    'them', 'see', 'other', 'than', 'then', 'now', 'look', 'only', 'come', 'its', 'over', 'think',
    'also', 'back', 'after', 'use', 'two', 'how', 'our', 'work', 'first', 'well', 'way', 'even',
    'new', 'want', 'because', 'any', 'these', 'give', 'day', 'most', 'us', 'is', 'was', 'are',
    'been', 'has', 'had', 'were', 'said', 'did', 'having', 'may', 'should', 'am', 'being', 'each',
    'such', 'during', 'here', 'where', 'both', 'few', 'more', 'very', 'through', 'between', 'under',
    'own', 'however', 'another', 'same', 'while', 'those', 'much', 'every', 'many', 'before', 'must',
    'too', 'does', 'part', 'once', 'least', 'made', 'over', 'still', 'since', 'per', 'thus', 'far',
    'yes', 'no', 'not', 'nor', 'yet', 'so', 'etc', 'eg', 'ie', 'via', 'vs', 'page', 'pages', 'fig',
    'figure', 'table', 'section', 'chapter', 'appendix', 'ref', 'references', 'note', 'notes',
    'right', 'alberta', 'province', 'queen', 'docx', 'majesty', 'copyright', 'printer', 'statutes',
    'regulations', 'format', 'print', 'electronic', 'belongs', 'consent', 'prior', 'person', 'reproduce',
    'copies', 'purpose', 'without', 'whether', 'standard', 'csa', 'class', 'ohs', 'workplace', 'health',
    'employer', 'employers', 'employee', 'employees', 'worker', 'workers', 'must', 'shall', 'should',
    'may', 'can', 'could', 'would', 'will', 'might', 'need', 'needs', 'required', 'requires',
}

def analyze_vocabulary(text, top_n=200):
    """Analyze vocabulary and return top N terms"""
    words = re.findall(r'\b[a-zA-Z]{3,}\b', text.lower())
    professional_words = [w for w in words if w not in STOP_WORDS]
    word_freq = Counter(professional_words)
    return word_freq.most_common(top_n)

def find_best_example(text, word, max_length=200):
    """Find the best example sentence for a word"""
    sentences = re.split(r'[.!?]+', text)

    # Find sentences with the word
    candidates = []
    for sentence in sentences:
        if re.search(r'\b' + re.escape(word) + r'\b', sentence, re.IGNORECASE):
            sentence = sentence.strip()
            if 30 < len(sentence) < max_length:
                candidates.append(sentence)

    # Return the first good candidate
    if candidates:
        return candidates[0]

    # Fallback to any sentence with the word
    for sentence in sentences:
        if re.search(r'\b' + re.escape(word) + r'\b', sentence, re.IGNORECASE):
            sentence = sentence.strip()
            if len(sentence) > 15:
                return sentence[:max_length] + "..." if len(sentence) > max_length else sentence

    return "Example not available"

def get_translation(word):
    """Get Chinese translation for a word"""
    return VOCABULARY_TRANSLATIONS.get(word.lower(), '【需要人工翻译】')

def generate_markdown_document(vocabulary_data, output_file):
    """Generate a comprehensive markdown document"""

    md_content = """# HET专业词汇大全
## Higher Education and Training Professional Vocabulary

**文档生成时间：** 2026-02-17
**数据来源：** 39个PDF文档分析
**总提取字符数：** 2,177,809
**词汇总数：** {total_words}

---

## 📚 目录

本文档包含从HET相关PDF文档中提取的高频专业词汇，按出现频率排序。每个词汇包含：
- 英文单词
- 中文翻译
- 出现频率
- 英文例句

---

## 📊 词汇列表

""".format(total_words=len(vocabulary_data))

    for item in vocabulary_data:
        rank = item['rank']
        word = item['word']
        freq = item['frequency']
        translation = item['translation']
        example = item['example']

        md_content += f"""
### {rank}. **{word.upper()}**

**中文：** {translation}
**频率：** {freq}次
**例句：** {example}

---
"""

    # Add footer
    md_content += """
## 📝 使用说明

1. **词汇分类：** 本文档中的词汇主要涉及汽车维修、职业健康安全、机械工程等领域
2. **翻译准确性：** 部分专业术语可能有多种翻译，请根据具体语境理解
3. **学习建议：** 建议结合例句理解词汇在实际工作场景中的应用

## 🔧 技术领域分类

本文档涵盖以下主要技术领域：
- 汽车制动系统 (Brake Systems)
- 液压系统 (Hydraulic Systems)
- 电气系统 (Electrical Systems)
- 发动机系统 (Engine Systems)
- 安全规范 (Safety Regulations)
- 维修工具 (Maintenance Tools)

---

**文档结束**
"""

    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(md_content)

    print(f"✓ Markdown document saved to: {output_file}")

def main():
    """Main function"""
    print("=" * 70)
    print("Comprehensive HET Vocabulary Analyzer")
    print("=" * 70)

    # Load text
    print("\nLoading extracted text...")
    text = load_extracted_text()
    print(f"✓ Loaded {len(text):,} characters")

    # Analyze vocabulary
    print("\nAnalyzing vocabulary (extracting top 200 terms)...")
    top_words = analyze_vocabulary(text, top_n=200)

    # Prepare data with translations and examples
    print("\nGenerating translations and examples...")
    vocabulary_data = []

    for rank, (word, freq) in enumerate(top_words, 1):
        translation = get_translation(word)
        example = find_best_example(text, word)

        vocabulary_data.append({
            'rank': rank,
            'word': word,
            'frequency': freq,
            'translation': translation,
            'example': example
        })

        if rank % 20 == 0:
            print(f"  Processed {rank}/200 words...")

    # Save to JSON
    json_file = Path("ocred/comprehensive_vocabulary.json")
    with open(json_file, 'w', encoding='utf-8') as f:
        json.dump(vocabulary_data, f, indent=2, ensure_ascii=False)
    print(f"\n✓ JSON data saved to: {json_file}")

    # Generate markdown document
    print("\nGenerating markdown document...")
    md_file = Path("HET专业词汇大全.md")
    generate_markdown_document(vocabulary_data, md_file)

    print("\n" + "=" * 70)
    print("✓ Analysis complete!")
    print(f"✓ Total vocabulary terms: {len(vocabulary_data)}")
    print(f"✓ Document saved to: {md_file}")
    print("=" * 70)

if __name__ == "__main__":
    main()

